// 投資類型
export type InvestmentType = 'simple' | 'compound' | 'regular';

// 利率類型
export type RateType = 'annual' | 'monthly';

// 複利頻率
export type CompoundFrequency = 'annually' | 'monthly' | 'daily';

// 定期定額頻率
export type RegularFrequency = 'monthly' | 'quarterly' | 'annually';

// 單利投資輸入參數
export interface SimpleInterestInput {
  principal: number; // 本金
  rate: number; // 利率 (%)
  rateType: RateType; // 利率類型
  period: number; // 投資期間 (年或月)
  periodType: 'years' | 'months'; // 期間類型
}

// 複利投資輸入參數
export interface CompoundInterestInput {
  principal: number; // 初始本金
  rate: number; // 年利率 (%)
  years: number; // 投資年期
  compoundFrequency: CompoundFrequency; // 複利頻率
}

// 定期定額投資輸入參數
export interface RegularInvestmentInput {
  initialAmount: number; // 初始本金
  regularAmount: number; // 每期投入金額
  rate: number; // 年利率 (%)
  years: number; // 投資年期
  frequency: RegularFrequency; // 投入頻率
}

// 投資時間點數據
export interface InvestmentTimePoint {
  period: number; // 期數
  principal: number; // 累積本金
  interest: number; // 累積利息
  total: number; // 總金額
  periodInterest?: number; // 當期利息
  periodContribution?: number; // 當期投入
}

// 單利投資結果
export interface SimpleInterestResult {
  finalAmount: number; // 到期本息總額
  totalInterest: number; // 總利息收入
  principal: number; // 本金
  returnRate: number; // 投資報酬率 (%)
  annualizedReturn: number; // 年化報酬率 (%)
  timePoints: InvestmentTimePoint[]; // 時間點數據
}

// 複利投資結果
export interface CompoundInterestResult {
  finalAmount: number; // 到期本息總額
  totalInterest: number; // 總利息收入
  principal: number; // 本金
  returnRate: number; // 投資報酬率 (%)
  annualizedReturn: number; // 年化報酬率 (%)
  compoundEffect: number; // 複利效應 (與單利的差額)
  timePoints: InvestmentTimePoint[]; // 時間點數據
  comparison: {
    simpleInterest: number; // 同條件下的單利結果
    compoundAdvantage: number; // 複利優勢 (%)
  };
}

// 定期定額投資結果
export interface RegularInvestmentResult {
  totalInvestment: number; // 總投入金額
  finalAmount: number; // 到期本息總額
  totalReturn: number; // 總獲利
  returnRate: number; // 投資報酬率 (%)
  annualizedReturn: number; // 年化報酬率 (%)
  timePoints: InvestmentTimePoint[]; // 時間點數據
  summary: {
    totalPeriods: number; // 總期數
    averageBalance: number; // 平均餘額
    effectiveRate: number; // 有效利率 (%)
  };
}

// 投資比較分析
export interface InvestmentComparison {
  simple: SimpleInterestResult;
  compound: CompoundInterestResult;
  regular?: RegularInvestmentResult;
  analysis: {
    bestOption: 'simple' | 'compound' | 'regular';
    compoundVsSimple: number; // 複利相對單利的優勢 (%)
    regularVsLumpSum?: number; // 定期定額相對一次性投資的差異 (%)
  };
}

// 表單驗證錯誤
export interface InvestmentFormErrors {
  principal?: string;
  rate?: string;
  period?: string;
  years?: string;
  initialAmount?: string;
  regularAmount?: string;
  compoundFrequency?: string;
  frequency?: string;
}

// 投資建議
export interface InvestmentAdvice {
  riskLevel: 'low' | 'medium' | 'high';
  recommendation: string;
  tips: string[];
  warnings?: string[];
}

// 圖表數據
export interface InvestmentChartData {
  labels: string[]; // X軸標籤 (時間)
  datasets: {
    principal: number[]; // 本金數據
    interest: number[]; // 利息數據
    total: number[]; // 總額數據
  };
  comparison?: {
    simple: number[]; // 單利比較數據
    compound: number[]; // 複利比較數據
    regular?: number[]; // 定期定額比較數據
  };
}
