'use client';

import { useState } from 'react';
import { Tab<PERSON>iew, Tab<PERSON>anel } from 'primereact/tabview';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import CreditLoanForm from '@/components/credit-loan/CreditLoanForm';
import CreditLoanSummary from '@/components/credit-loan/CreditLoanSummary';
import CreditLoanTable from '@/components/credit-loan/CreditLoanTable';
import RateReverseForm from '@/components/credit-loan/RateReverseForm';
import RateReverseResult from '@/components/credit-loan/RateReverseResult';
import { CreditLoanInput, CreditLoanResult, RateReverseInput, RateReverseResult as RateReverseResultType } from '@/lib/types/creditLoan';
import { calculateCreditLoan } from '@/lib/calculations/creditLoan';
import { calculateRateReverse } from '@/lib/calculations/rateReverse';

export default function CreditLoanPage() {
  const [result, setResult] = useState<CreditLoanResult | null>(null);
  const [rateReverseResult, setRateReverseResult] = useState<RateReverseResultType | null>(null);
  const [loading, setLoading] = useState(false);
  const [rateReverseLoading, setRateReverseLoading] = useState(false);

  const handleCalculate = async (input: CreditLoanInput) => {
    setLoading(true);
    try {
      // 模擬計算延遲
      await new Promise(resolve => setTimeout(resolve, 300));

      const calculationResult = calculateCreditLoan(input);
      setResult(calculationResult);
    } catch (error) {
      console.error('計算錯誤:', error);
      // 這裡可以添加錯誤處理
    } finally {
      setLoading(false);
    }
  };

  const handleRateReverseCalculate = async (input: RateReverseInput) => {
    setRateReverseLoading(true);
    try {
      // 模擬計算延遲
      await new Promise(resolve => setTimeout(resolve, 300));

      const calculationResult = calculateRateReverse(input);
      setRateReverseResult(calculationResult);
    } catch (error) {
      console.error('利率回推計算錯誤:', error);
      // 這裡可以添加錯誤處理
    } finally {
      setRateReverseLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-1 py-4">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800">信貸計算</h1>

          <TabView renderActiveOnly={false}>
            {/* 正向計算標籤頁 */}
            <TabPanel header="正向計算" leftIcon="pi pi-calculator mr-2">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                {/* 表單區域 */}
                <div>
                  <h2 className="text-xl font-semibold mb-6">貸款資訊</h2>
                  <CreditLoanForm onCalculate={handleCalculate} loading={loading} />
                </div>

                {/* 結果區域 */}
                <div>
                  <h2 className="text-xl font-semibold mb-6">計算結果</h2>
                  <CreditLoanSummary result={result} loading={loading} />
                </div>
              </div>

              {/* 還款計劃表 */}
              <div className="mt-8">
                <h2 className="text-xl font-semibold mb-6">還款計劃表</h2>
                <CreditLoanTable
                  payments={result?.monthlyPayments || []}
                  loading={loading}
                />
              </div>
            </TabPanel>

            {/* 利率回推標籤頁 */}
            <TabPanel header="利率回推" leftIcon="pi pi-search mr-2">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                {/* 表單區域 */}
                <div>
                  <h2 className="text-xl font-semibold mb-6">已知條件</h2>
                  <RateReverseForm onCalculate={handleRateReverseCalculate} loading={rateReverseLoading} />
                </div>

                {/* 結果區域 */}
                <div>
                  <h2 className="text-xl font-semibold mb-6">利率分析</h2>
                  <RateReverseResult result={rateReverseResult} loading={rateReverseLoading} />
                </div>
              </div>
            </TabPanel>
          </TabView>
        </div>
      </main>

      <Footer />
    </div>
  );
}