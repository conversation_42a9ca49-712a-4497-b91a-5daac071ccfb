'use client';

import { useState, useEffect } from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Message } from 'primereact/message';
import { RadioButton } from 'primereact/radiobutton';
import { MortgageInput } from '@/lib/types/mortgage';
import { validateMortgageInput, calculateDownPayment, calculateLoanToValueRatio } from '@/lib/calculations/mortgage';
import { formatCurrency } from '@/lib/calculations/utils';

interface MortgageFormProps {
  onCalculate: (input: MortgageInput) => void;
  loading?: boolean;
}

export default function MortgageForm({ onCalculate, loading = false }: MortgageFormProps) {
  const [formData, setFormData] = useState<MortgageInput>({
    housePrice: 10000000,
    downPaymentType: 'percentage',
    downPaymentPercentage: 20,
    loanAmount: 8000000,
    loanYears: 20,
    rateType: 'fixed',
    interestRate: 2.5,
    graceYears: 0,
    fees: {
      notaryFee: 15000,
      registrationFeeRate: 0.1,
      fireInsuranceAnnual: 3000,
      earthquakeInsuranceAnnual: 1500,
      appraisalFee: 8000
    }
  });

  const [errors, setErrors] = useState<string[]>([]);

  const loanYearOptions = [
    { label: '10年', value: 10 },
    { label: '15年', value: 15 },
    { label: '20年', value: 20 },
    { label: '25年', value: 25 },
    { label: '30年', value: 30 },
    { label: '35年', value: 35 },
    { label: '40年', value: 40 }
  ];

  const graceYearOptions = [
    { label: '無寬限期', value: 0 },
    { label: '1年', value: 1 },
    { label: '2年', value: 2 },
    { label: '3年', value: 3 },
    { label: '4年', value: 4 },
    { label: '5年', value: 5 }
  ];

  const rateTypeOptions = [
    { label: '固定利率', value: 'fixed' },
    { label: '機動利率', value: 'floating' }
  ];

  // 計算貸款金額
  useEffect(() => {
    const downPayment = calculateDownPayment(
      formData.housePrice,
      formData.downPaymentType,
      formData.downPaymentAmount,
      formData.downPaymentPercentage
    );
    const loanAmount = formData.housePrice - downPayment;

    setFormData(prev => ({
      ...prev,
      loanAmount: Math.max(0, loanAmount)
    }));
  }, [formData.housePrice, formData.downPaymentType, formData.downPaymentAmount, formData.downPaymentPercentage]);

  const handleSubmit = () => {
    const validationErrors = validateMortgageInput(formData);
    setErrors(validationErrors);

    if (validationErrors.length === 0) {
      onCalculate(formData);
    }
  };

  const updateFormData = (field: string, value: number | string | null | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateNestedFormData = (parentField: string, childField: string, value: number | null | undefined) => {
    setFormData(prev => {
      if (parentField === 'fees') {
        return {
          ...prev,
          fees: {
            ...prev.fees,
            [childField]: value || 0
          }
        };
      }
      return prev;
    });
  };

  // 計算貸款成數
  const loanToValueRatio = calculateLoanToValueRatio(formData.housePrice, formData.loanAmount);

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 錯誤訊息 */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <Message key={index} severity="error" text={error} className="w-full" />
            ))}
          </div>
        )}

        {/* 房屋資訊 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">房屋資訊</h3>

          <div className="field">
            <label htmlFor="housePrice" className="block text-sm font-medium mb-2">
              房屋總價 (元)
            </label>
            <InputNumber
              id="housePrice"
              value={formData.housePrice}
              onValueChange={(e) => updateFormData('housePrice', e.value)}
              mode="currency"
              currency="TWD"
              locale="zh-TW"
              min={100000}
              max={500000000}
              minFractionDigits={0}
              maxFractionDigits={0}
              useGrouping={true}
              className="w-full"
            />
          </div>

          {/* 自備款設定 */}
          <div className="space-y-3">
            <label className="block text-sm font-medium">自備款設定</label>

            <div className="flex gap-6">
              <div className="flex items-center">
                <RadioButton
                  inputId="downPaymentAmount"
                  name="downPaymentType"
                  value="amount"
                  onChange={(e) => updateFormData('downPaymentType', e.value)}
                  checked={formData.downPaymentType === 'amount'}
                />
                <label htmlFor="downPaymentAmount" className="ml-2">金額</label>
              </div>
              <div className="flex items-center">
                <RadioButton
                  inputId="downPaymentPercentage"
                  name="downPaymentType"
                  value="percentage"
                  onChange={(e) => updateFormData('downPaymentType', e.value)}
                  checked={formData.downPaymentType === 'percentage'}
                />
                <label htmlFor="downPaymentPercentage" className="ml-2">百分比</label>
              </div>
            </div>

            {formData.downPaymentType === 'amount' ? (
              <div className="field">
                <InputNumber
                  value={formData.downPaymentAmount}
                  onValueChange={(e) => updateFormData('downPaymentAmount', e.value)}
                  mode="currency"
                  currency="TWD"
                  locale="zh-TW"
                  min={0}
                  max={formData.housePrice}
                  minFractionDigits={0}
                  maxFractionDigits={0}
                  useGrouping={true}
                  className="w-full"
                  placeholder="請輸入自備款金額"
                />
              </div>
            ) : (
              <div className="field">
                <InputNumber
                  value={formData.downPaymentPercentage}
                  onValueChange={(e) => updateFormData('downPaymentPercentage', e.value)}
                  mode="decimal"
                  minFractionDigits={1}
                  maxFractionDigits={2}
                  min={0}
                  max={99}
                  suffix="%"
                  className="w-full"
                  placeholder="請輸入自備款百分比"
                />
              </div>
            )}
          </div>

          {/* 貸款金額顯示 */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex-items gap-12">
              <div>
                <div className="text-blue-600 font-medium">貸款金額</div>
                <div className="text-xl font-bold text-blue-800">
                  {formatCurrency(formData.loanAmount)}
                </div>
              </div>
              <div>
                <div className="text-blue-600 font-medium">貸款成數</div>
                <div className="text-xl font-bold text-blue-800">
                  {loanToValueRatio.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* 貸款條件 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">貸款條件</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="field">
              <label htmlFor="loanYears" className="block text-sm font-medium mb-2">
                貸款年限
              </label>
              <Dropdown
                id="loanYears"
                value={formData.loanYears}
                options={loanYearOptions}
                onChange={(e) => updateFormData('loanYears', e.value)}
                className="w-full"
              />
            </div>

            <div className="field">
              <label htmlFor="graceYears" className="block text-sm font-medium mb-2">
                寬限期
              </label>
              <Dropdown
                id="graceYears"
                value={formData.graceYears}
                options={graceYearOptions}
                onChange={(e) => updateFormData('graceYears', e.value)}
                className="w-full"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="field">
              <label htmlFor="rateType" className="block text-sm font-medium mb-2">
                利率類型
              </label>
              <Dropdown
                id="rateType"
                value={formData.rateType}
                options={rateTypeOptions}
                onChange={(e) => updateFormData('rateType', e.value)}
                className="w-full"
              />
            </div>

            <div className="field">
              <label htmlFor="interestRate" className="block text-sm font-medium mb-2">
                年利率 (%)
              </label>
              <InputNumber
                id="interestRate"
                value={formData.interestRate}
                onValueChange={(e) => updateFormData('interestRate', e.value)}
                mode="decimal"
                minFractionDigits={2}
                maxFractionDigits={4}
                min={0}
                max={30}
                suffix="%"
                className="w-full"
              />
            </div>
          </div>
        </div>

        <Divider />

        {/* 相關費用 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">相關費用</h3>

          {/* 第一行：代書費、規費、鑑價費 */}
          <div className="flex-items gap-4">
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                代書費 (元)
              </label>
              <InputNumber
                value={formData.fees.notaryFee}
                onValueChange={(e) => updateNestedFormData('fees', 'notaryFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                規費 (%)
              </label>
              <InputNumber
                value={formData.fees.registrationFeeRate}
                onValueChange={(e) => updateNestedFormData('fees', 'registrationFeeRate', e.value)}
                mode="decimal"
                minFractionDigits={2}
                maxFractionDigits={3}
                min={0}
                max={10}
                suffix="%"
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                鑑價費 (元)
              </label>
              <InputNumber
                value={formData.fees.appraisalFee}
                onValueChange={(e) => updateNestedFormData('fees', 'appraisalFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>
          </div>

          {/* 第二行：保險費用 */}
          <div className="flex-items gap-4">
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                火險費 (年費)
              </label>
              <InputNumber
                value={formData.fees.fireInsuranceAnnual}
                onValueChange={(e) => updateNestedFormData('fees', 'fireInsuranceAnnual', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                地震險 (年費)
              </label>
              <InputNumber
                value={formData.fees.earthquakeInsuranceAnnual}
                onValueChange={(e) => updateNestedFormData('fees', 'earthquakeInsuranceAnnual', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* 計算按鈕 */}
        <div className="flex justify-center pt-4">
          <Button
            label="開始計算"
            icon="pi pi-calculator"
            onClick={handleSubmit}
            loading={loading}
            size="large"
            className="px-8"
          />
        </div>
      </div>
    </Card>
  );
}
