import { RateReverseInput, RateReverseResult } from '../types/creditLoan';

/**
 * 計算等額本息還款的月付金
 * @param principal 本金
 * @param monthlyRate 月利率
 * @param months 期數
 * @returns 月付金
 */
function calculateMonthlyPayment(principal: number, monthlyRate: number, months: number): number {
  if (monthlyRate === 0) {
    return principal / months;
  }

  const factor = Math.pow(1 + monthlyRate, months);
  return principal * (monthlyRate * factor) / (factor - 1);
}

/**
 * 計算給定利率下的總月付金 (包含費用)
 * @param principal 本金
 * @param annualRate 年利率 (%)
 * @param months 期數
 * @param monthlyFee 每月費用
 * @returns 總月付金
 */
function calculateTotalMonthlyPayment(
  principal: number,
  annualRate: number,
  months: number,
  monthlyFee: number
): number {
  const monthlyRate = annualRate / 100 / 12;
  const basePayment = calculateMonthlyPayment(principal, monthlyRate, months);
  return basePayment + monthlyFee;
}

/**
 * 使用牛頓法求解利率
 * @param input 利率回推輸入參數
 * @returns 名目年利率 (%)
 */
function solveRateNewton(input: RateReverseInput): { rate: number; iterations: number; accuracy: number } {
  const { loanAmount, monthlyPayment, termMonths, fees } = input;
  const targetPayment = monthlyPayment;
  const monthlyFee = fees.monthlyFee;

  // 初始猜測值 (年利率 5%)
  let rate = 5.0;
  let iterations = 0;
  const maxIterations = 100;
  const tolerance = 1e-8;

  for (let i = 0; i < maxIterations; i++) {
    iterations = i + 1;

    // 計算當前利率下的月付金
    const currentPayment = calculateTotalMonthlyPayment(loanAmount, rate, termMonths, monthlyFee);

    // 計算誤差
    const error = currentPayment - targetPayment;

    // 檢查收斂
    if (Math.abs(error) < tolerance) {
      return { rate, iterations, accuracy: Math.abs(error) };
    }

    // 計算導數 (數值微分)
    const delta = 0.0001;
    const paymentPlus = calculateTotalMonthlyPayment(loanAmount, rate + delta, termMonths, monthlyFee);
    const derivative = (paymentPlus - currentPayment) / delta;

    // 避免除零
    if (Math.abs(derivative) < 1e-12) {
      break;
    }

    // 牛頓法更新
    const newRate = rate - error / derivative;

    // 限制利率範圍 (0.01% - 50%)
    rate = Math.max(0.01, Math.min(50, newRate));
  }

  return { rate, iterations, accuracy: Infinity };
}

/**
 * 使用二分法求解利率 (備用方法)
 * @param input 利率回推輸入參數
 * @returns 名目年利率 (%)
 */
function solveRateBisection(input: RateReverseInput): { rate: number; iterations: number; accuracy: number } {
  const { loanAmount, monthlyPayment, termMonths, fees } = input;
  const targetPayment = monthlyPayment;
  const monthlyFee = fees.monthlyFee;

  let lowRate = 0.01;
  let highRate = 50.0;
  let iterations = 0;
  const maxIterations = 100;
  const tolerance = 1e-6;

  // 檢查邊界條件
  const lowPayment = calculateTotalMonthlyPayment(loanAmount, lowRate, termMonths, monthlyFee);
  const highPayment = calculateTotalMonthlyPayment(loanAmount, highRate, termMonths, monthlyFee);

  if (targetPayment < lowPayment || targetPayment > highPayment) {
    return { rate: NaN, iterations: 0, accuracy: Infinity };
  }

  for (let i = 0; i < maxIterations; i++) {
    iterations = i + 1;

    const midRate = (lowRate + highRate) / 2;
    const midPayment = calculateTotalMonthlyPayment(loanAmount, midRate, termMonths, monthlyFee);

    const error = Math.abs(midPayment - targetPayment);

    if (error < tolerance) {
      return { rate: midRate, iterations, accuracy: error };
    }

    if (midPayment < targetPayment) {
      lowRate = midRate;
    } else {
      highRate = midRate;
    }
  }

  return { rate: (lowRate + highRate) / 2, iterations, accuracy: Infinity };
}

/**
 * 評估利率水準
 * @param rate 年利率 (%)
 * @returns 利率評估
 */
function assessRate(rate: number): { level: 'low' | 'medium' | 'high' | 'very-high'; description: string } {
  if (rate < 3) {
    return { level: 'low', description: '利率偏低，條件優惠' };
  } else if (rate < 6) {
    return { level: 'medium', description: '利率合理，市場水準' };
  } else if (rate < 12) {
    return { level: 'high', description: '利率偏高，建議比較其他方案' };
  } else {
    return { level: 'very-high', description: '利率過高，請謹慎評估' };
  }
}

/**
 * 計算實際年利率 (APR)
 * @param input 利率回推輸入參數
 * @param nominalRate 名目年利率
 * @returns 實際年利率 (%)
 */
function calculateAPR(input: RateReverseInput, nominalRate: number): number {
  const { loanAmount, termMonths, fees } = input;

  // 計算總費用
  // const totalFees = fees.setupFee + fees.monthlyFee * termMonths + fees.otherFees;

  // 實際借到的金額 (扣除開辦費和其他一次性費用)
  const netLoanAmount = loanAmount - fees.setupFee - fees.otherFees;

  // 月付金 (不含帳管費)
  const monthlyRate = nominalRate / 100 / 12;
  const baseMonthlyPayment = calculateMonthlyPayment(loanAmount, monthlyRate, termMonths);

  // 使用 IRR 方法計算 APR
  // 現金流：初期收到 netLoanAmount，每月支付 baseMonthlyPayment + monthlyFee
  const monthlyPaymentWithFee = baseMonthlyPayment + fees.monthlyFee;

  // 簡化的 APR 計算 (使用總成本法)
  const totalPayments = monthlyPaymentWithFee * termMonths;
  const totalCost = totalPayments - netLoanAmount;
  const averageBalance = netLoanAmount / 2;
  const years = termMonths / 12;

  if (averageBalance === 0 || years === 0) return nominalRate;

  return (totalCost / averageBalance / years) * 100;
}

/**
 * 利率回推主函數
 * @param input 利率回推輸入參數
 * @returns 利率回推結果
 */
export function calculateRateReverse(input: RateReverseInput): RateReverseResult {
  // 首先嘗試牛頓法
  let result = solveRateNewton(input);

  // 如果牛頓法失敗，使用二分法
  if (!isFinite(result.rate) || result.accuracy > 1e-6) {
    result = solveRateBisection(input);
  }

  const nominalRate = result.rate;
  const convergenceSuccess = isFinite(nominalRate) && result.accuracy < 1e-6;

  // 計算相關數據
  const monthlyRate = nominalRate / 100 / 12;
  const baseMonthlyPayment = calculateMonthlyPayment(input.loanAmount, monthlyRate, input.termMonths);
  const totalInterest = (baseMonthlyPayment * input.termMonths) - input.loanAmount;
  const totalFees = input.fees.setupFee + input.fees.monthlyFee * input.termMonths + input.fees.otherFees;
  const totalAmount = input.loanAmount + totalInterest + totalFees;

  // 計算 APR
  const effectiveRate = convergenceSuccess ? calculateAPR(input, nominalRate) : nominalRate;

  // 評估利率
  const rateAssessment = assessRate(nominalRate);

  return {
    nominalRate: convergenceSuccess ? nominalRate : 0,
    effectiveRate: convergenceSuccess ? effectiveRate : 0,
    totalInterest: convergenceSuccess ? totalInterest : 0,
    totalFees,
    totalAmount: convergenceSuccess ? totalAmount : 0,
    rateAssessment,
    convergence: {
      success: convergenceSuccess,
      iterations: result.iterations,
      accuracy: result.accuracy
    }
  };
}

/**
 * 驗證利率回推輸入參數
 * @param input 利率回推輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateRateReverseInput(input: Partial<RateReverseInput>): string[] {
  const errors: string[] = [];

  if (!input.loanAmount || input.loanAmount <= 0) {
    errors.push('貸款金額必須大於 0');
  }

  if (!input.monthlyPayment || input.monthlyPayment <= 0) {
    errors.push('月付金額必須大於 0');
  }

  if (!input.termMonths || input.termMonths <= 0 || input.termMonths > 360) {
    errors.push('期數必須在 1-360 個月之間');
  }

  // 檢查月付金是否合理
  if (input.loanAmount && input.monthlyPayment && input.termMonths) {
    const minMonthlyPayment = input.loanAmount / input.termMonths;
    const totalFees = (input.fees?.setupFee || 0) +
      ((input.fees?.monthlyFee || 0) * input.termMonths) +
      (input.fees?.otherFees || 0);
    const maxReasonablePayment = (input.loanAmount + totalFees) / input.termMonths * 3; // 假設最高利率約 200%

    if (input.monthlyPayment < minMonthlyPayment) {
      errors.push('月付金額過低，無法償還本金');
    }

    if (input.monthlyPayment > maxReasonablePayment) {
      errors.push('月付金額過高，可能無法計算出合理利率');
    }
  }

  // 驗證費用
  if (input.fees) {
    if (input.fees.setupFee < 0) {
      errors.push('開辦費不能為負數');
    }
    if (input.fees.monthlyFee < 0) {
      errors.push('帳管費不能為負數');
    }
    if (input.fees.otherFees < 0) {
      errors.push('其他費用不能為負數');
    }
  }

  return errors;
}
