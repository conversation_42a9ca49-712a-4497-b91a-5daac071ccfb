'use client';

import { useState } from 'react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import InvestmentForm from '@/components/investment/InvestmentForm';
import InvestmentResult from '@/components/investment/InvestmentResult';
import {
  InvestmentType,
  SimpleInterestInput,
  CompoundInterestInput,
  RegularInvestmentInput,
  SimpleInterestResult,
  CompoundInterestResult,
  RegularInvestmentResult
} from '@/lib/types/investment';
import {
  calculateSimpleInterest,
  calculateCompoundInterest,
  calculateRegularInvestment
} from '@/lib/calculations/investment';

export default function InvestmentPage() {
  const [result, setResult] = useState<SimpleInterestResult | CompoundInterestResult | RegularInvestmentResult | null>(null);
  const [investmentType, setInvestmentType] = useState<InvestmentType | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCalculate = async (
    type: InvestmentType,
    input: SimpleInterestInput | CompoundInterestInput | RegularInvestmentInput
  ) => {
    setLoading(true);
    try {
      // 模擬計算延遲
      await new Promise(resolve => setTimeout(resolve, 300));
      
      let calculationResult: SimpleInterestResult | CompoundInterestResult | RegularInvestmentResult;
      
      switch (type) {
        case 'simple':
          calculationResult = calculateSimpleInterest(input as SimpleInterestInput);
          break;
        case 'compound':
          calculationResult = calculateCompoundInterest(input as CompoundInterestInput);
          break;
        case 'regular':
          calculationResult = calculateRegularInvestment(input as RegularInvestmentInput);
          break;
        default:
          throw new Error('未知的投資類型');
      }
      
      setResult(calculationResult);
      setInvestmentType(type);
    } catch (error) {
      console.error('投資計算錯誤:', error);
      // 這裡可以添加錯誤處理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">投資回報計算</h1>
          
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* 表單區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">投資參數</h2>
              <InvestmentForm onCalculate={handleCalculate} loading={loading} />
            </div>

            {/* 結果區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">計算結果</h2>
              <InvestmentResult 
                type={investmentType} 
                result={result} 
                loading={loading} 
              />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
