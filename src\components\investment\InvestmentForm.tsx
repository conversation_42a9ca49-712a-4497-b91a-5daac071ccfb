'use client';

import { useState } from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Message } from 'primereact/message';
import { RadioButton } from 'primereact/radiobutton';
import {
  InvestmentType,
  SimpleInterestInput,
  CompoundInterestInput,
  RegularInvestmentInput,
  RateType,
  CompoundFrequency,
  RegularFrequency
} from '@/lib/types/investment';
import {
  validateSimpleInterestInput,
  validateCompoundInterestInput,
  validateRegularInvestmentInput
} from '@/lib/calculations/investment';

interface InvestmentFormProps {
  onCalculate: (
    type: InvestmentType,
    input: SimpleInterestInput | CompoundInterestInput | RegularInvestmentInput
  ) => void;
  loading?: boolean;
}

export default function InvestmentForm({ onCalculate, loading = false }: InvestmentFormProps) {
  const [investmentType, setInvestmentType] = useState<InvestmentType>('compound');
  const [errors, setErrors] = useState<string[]>([]);

  // 單利表單數據
  const [simpleData, setSimpleData] = useState<SimpleInterestInput>({
    principal: 1000000,
    rate: 3.5,
    rateType: 'annual',
    period: 5,
    periodType: 'years'
  });

  // 複利表單數據
  const [compoundData, setCompoundData] = useState<CompoundInterestInput>({
    principal: 1000000,
    rate: 5.0,
    years: 10,
    compoundFrequency: 'monthly'
  });

  // 定期定額表單數據
  const [regularData, setRegularData] = useState<RegularInvestmentInput>({
    initialAmount: 100000,
    regularAmount: 10000,
    rate: 6.0,
    years: 10,
    frequency: 'monthly'
  });

  const rateTypeOptions = [
    { label: '年利率', value: 'annual' },
    { label: '月利率', value: 'monthly' }
  ];

  const periodTypeOptions = [
    { label: '年', value: 'years' },
    { label: '月', value: 'months' }
  ];

  const compoundFrequencyOptions = [
    { label: '年複利', value: 'annually' },
    { label: '月複利', value: 'monthly' },
    { label: '日複利', value: 'daily' }
  ];

  const regularFrequencyOptions = [
    { label: '每月', value: 'monthly' },
    { label: '每季', value: 'quarterly' },
    { label: '每年', value: 'annually' }
  ];

  const handleSubmit = () => {
    let validationErrors: string[] = [];
    let inputData: SimpleInterestInput | CompoundInterestInput | RegularInvestmentInput;

    switch (investmentType) {
      case 'simple':
        validationErrors = validateSimpleInterestInput(simpleData);
        inputData = simpleData;
        break;
      case 'compound':
        validationErrors = validateCompoundInterestInput(compoundData);
        inputData = compoundData;
        break;
      case 'regular':
        validationErrors = validateRegularInvestmentInput(regularData);
        inputData = regularData;
        break;
      default:
        validationErrors = ['請選擇投資類型'];
        return;
    }

    setErrors(validationErrors);

    if (validationErrors.length === 0) {
      onCalculate(investmentType, inputData);
    }
  };

  const updateSimpleData = (field: string, value: number | string | null | undefined) => {
    setSimpleData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateCompoundData = (field: string, value: number | string | null | undefined) => {
    setCompoundData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateRegularData = (field: string, value: number | string | null | undefined) => {
    setRegularData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  {/* 單利 */ }
  const renderSimpleForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            本金 (元)
          </label>
          <InputNumber
            value={simpleData.principal}
            onValueChange={(e) => updateSimpleData('principal', e.value)}
            mode="currency"
            currency="TWD"
            locale="zh-TW"
            min={1}
            minFractionDigits={0}
            maxFractionDigits={0}
            useGrouping={true}
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            利率 (%)
          </label>
          <InputNumber
            value={simpleData.rate}
            onValueChange={(e) => updateSimpleData('rate', e.value)}
            mode="decimal"
            minFractionDigits={2}
            maxFractionDigits={4}
            min={0}
            max={100}
            suffix="%"
            className="w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            利率類型
          </label>
          <Dropdown
            value={simpleData.rateType}
            options={rateTypeOptions}
            onChange={(e) => updateSimpleData('rateType', e.value)}
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            期間類型
          </label>
          <Dropdown
            value={simpleData.periodType}
            options={periodTypeOptions}
            onChange={(e) => updateSimpleData('periodType', e.value)}
            className="w-full"
          />
        </div>
      </div>

      <div className="field">
        <label className="block text-sm font-medium mb-2">
          投資期間 ({simpleData.periodType === 'years' ? '年' : '月'})
        </label>
        <InputNumber
          value={simpleData.period}
          onValueChange={(e) => updateSimpleData('period', e.value)}
          min={1}
          max={simpleData.periodType === 'years' ? 50 : 600}
          className="w-full"
        />
      </div>
    </div>
  );
  {/* 複利 */ }
  const renderCompoundForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            初始本金 (元)
          </label>
          <InputNumber
            value={compoundData.principal}
            onValueChange={(e) => updateCompoundData('principal', e.value)}
            mode="currency"
            currency="TWD"
            locale="zh-TW"
            min={1}
            minFractionDigits={0}
            maxFractionDigits={0}
            useGrouping={true}
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            年利率 (%)
          </label>
          <InputNumber
            value={compoundData.rate}
            onValueChange={(e) => updateCompoundData('rate', e.value)}
            mode="decimal"
            minFractionDigits={2}
            maxFractionDigits={4}
            min={0}
            max={100}
            suffix="%"
            className="w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            投資年期 (年)
          </label>
          <InputNumber
            value={compoundData.years}
            onValueChange={(e) => updateCompoundData('years', e.value)}
            min={1}
            max={50}
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            複利頻率
          </label>
          <Dropdown
            value={compoundData.compoundFrequency}
            options={compoundFrequencyOptions}
            onChange={(e) => updateCompoundData('compoundFrequency', e.value)}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );

  {/* 定期定額 */ }
  const renderRegularForm = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            初始本金 (元)
          </label>
          <InputNumber
            value={regularData.initialAmount}
            onValueChange={(e) => updateRegularData('initialAmount', e.value)}
            mode="currency"
            currency="TWD"
            locale="zh-TW"
            min={0}
            minFractionDigits={0}
            maxFractionDigits={0}
            useGrouping={true}
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            每期投入金額 (元)
          </label>
          <InputNumber
            value={regularData.regularAmount}
            onValueChange={(e) => updateRegularData('regularAmount', e.value)}
            mode="currency"
            currency="TWD"
            locale="zh-TW"
            min={0}
            minFractionDigits={0}
            maxFractionDigits={0}
            useGrouping={true}
            className="w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="field">
          <label className="block text-sm font-medium mb-2">
            年利率 (%)
          </label>
          <InputNumber
            value={regularData.rate}
            onValueChange={(e) => updateRegularData('rate', e.value)}
            mode="decimal"
            minFractionDigits={2}
            maxFractionDigits={4}
            min={0}
            max={100}
            suffix="%"
            className="w-full"
          />
        </div>

        <div className="field">
          <label className="block text-sm font-medium mb-2">
            投入頻率
          </label>
          <Dropdown
            value={regularData.frequency}
            options={regularFrequencyOptions}
            onChange={(e) => updateRegularData('frequency', e.value)}
            className="w-full"
          />
        </div>
      </div>

      <div className="field">
        <label className="block text-sm font-medium mb-2">
          投資年期 (年)
        </label>
        <InputNumber
          value={regularData.years}
          onValueChange={(e) => updateRegularData('years', e.value)}
          min={1}
          max={50}
          className="w-full"
        />
      </div>
    </div>
  );

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 功能說明 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-green-600 mt-1"></i>
            <div className="text-sm text-green-800">
              <p className="font-medium mb-1">投資回報計算</p>
              <p>計算不同投資方式的預期回報，幫助您做出最佳投資決策</p>
            </div>
          </div>
        </div>

        {/* 錯誤訊息 */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <Message key={index} severity="error" text={error} className="w-full" />
            ))}
          </div>
        )}

        {/* 投資類型選擇 */}
        <div className="space-y-3">
          <label className="block text-sm font-medium">投資類型</label>

          <div className="flex flex-wrap gap-6">
            <div className="flex items-center">
              <RadioButton
                inputId="simple"
                name="investmentType"
                value="simple"
                onChange={(e) => setInvestmentType(e.value)}
                checked={investmentType === 'simple'}
              />
              <label htmlFor="simple" className="ml-2">單利計算</label>
            </div>
            <div className="flex items-center">
              <RadioButton
                inputId="compound"
                name="investmentType"
                value="compound"
                onChange={(e) => setInvestmentType(e.value)}
                checked={investmentType === 'compound'}
              />
              <label htmlFor="compound" className="ml-2">複利計算</label>
            </div>
            <div className="flex items-center">
              <RadioButton
                inputId="regular"
                name="investmentType"
                value="regular"
                onChange={(e) => setInvestmentType(e.value)}
                checked={investmentType === 'regular'}
              />
              <label htmlFor="regular" className="ml-2">定期定額</label>
            </div>
          </div>
        </div>

        <Divider />

        {/* 動態表單 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            {investmentType === 'simple' && '單利投資參數'}
            {investmentType === 'compound' && '複利投資參數'}
            {investmentType === 'regular' && '定期定額參數'}
          </h3>

          {investmentType === 'simple' && renderSimpleForm()}
          {investmentType === 'compound' && renderCompoundForm()}
          {investmentType === 'regular' && renderRegularForm()}
        </div>

        {/* 計算按鈕 */}
        <div className="flex justify-center pt-4">
          <Button
            label="開始計算"
            icon="pi pi-calculator"
            onClick={handleSubmit}
            loading={loading}
            size="large"
            className="px-8"
          />
        </div>
      </div>
    </Card>
  );
}
