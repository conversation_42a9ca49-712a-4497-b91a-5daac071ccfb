'use client';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Toolbar } from 'primereact/toolbar';
import { Tag } from 'primereact/tag';
import { MortgageMonthlyPayment } from '@/lib/types/mortgage';
import { formatCurrency } from '@/lib/calculations/utils';

interface MortgageTableProps {
  payments: MortgageMonthlyPayment[];
  loading?: boolean;
}
export default function MortgageTable({ payments, loading = false }: MortgageTableProps) {

  // 格式化數字顯示
  const formatCurrencyValue = (value: number) => {
    return formatCurrency(value);
  };

  // 期數欄位模板
  const monthTemplate = (rowData: MortgageMonthlyPayment) => {
    return (
      <div className="text-center font-medium">
        第 {rowData.month} 期
        {rowData.isGracePeriod && (
          <Tag value="寬限期" severity="warning" className="ml-2" />
        )}
      </div>
    );
  };

  // 利息欄位模板
  const interestTemplate = (rowData: MortgageMonthlyPayment) => {
    return (
      <div className="text-right text-red-600 font-medium">
        {formatCurrencyValue(rowData.interest)}
      </div>
    );
  };

  // 本金欄位模板
  const principalTemplate = (rowData: MortgageMonthlyPayment) => {
    return (
      <div className="text-right text-blue-600 font-medium">
        {rowData.isGracePeriod ? (
          <span className="text-gray-400">-</span>
        ) : (
          formatCurrencyValue(rowData.principal)
        )}
      </div>
    );
  };

  // 本息合計欄位模板
  const totalPaymentTemplate = (rowData: MortgageMonthlyPayment) => {
    return (
      <div className={`text-right font-semibold ${rowData.isGracePeriod ? 'text-orange-600' : 'text-gray-800'}`}>
        {formatCurrencyValue(rowData.totalPayment)}
      </div>
    );
  };

  // 剩餘本金欄位模板
  const remainingBalanceTemplate = (rowData: MortgageMonthlyPayment) => {
    return (
      <div className="text-right text-gray-600">
        {formatCurrencyValue(rowData.remainingBalance)}
      </div>
    );
  };

  // 匯出 CSV
  const exportCSV = () => {
    const csvData = payments.map(payment => ({
      '期數': payment.month,
      '寬限期': payment.isGracePeriod ? '是' : '否',
      '本期應繳利息': payment.interest,
      '本期應還本金': payment.principal,
      '本息合計': payment.totalPayment,
      '剩餘本金': payment.remainingBalance
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `房貸還款計劃_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 工具列左側
  const leftToolbarTemplate = () => {
    return (
      <div className="flex flex-wrap gap-2">
        <Button
          label="匯出 CSV"
          icon="pi pi-download"
          onClick={exportCSV}
          className="p-button-success"
          size="small"
          disabled={payments.length === 0}
        />
      </div>
    );
  };



  // 表格頁尾模板
  const footerTemplate = () => {
    if (payments.length === 0) return null;

    const totalInterest = payments.reduce((sum, payment) => sum + payment.interest, 0);
    const totalPrincipal = payments.reduce((sum, payment) => sum + payment.principal, 0);
    const totalPayments = payments.reduce((sum, payment) => sum + payment.totalPayment, 0);
    const gracePayments = payments.filter(p => p.isGracePeriod);
    const normalPayments = payments.filter(p => !p.isGracePeriod);

    return (
      <div className="bg-gray-50 p-4 border-t">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="text-gray-600">總利息</div>
            <div className="font-bold text-red-600">{formatCurrencyValue(totalInterest)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">總本金</div>
            <div className="font-bold text-blue-600">{formatCurrencyValue(totalPrincipal)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">總本息</div>
            <div className="font-bold text-gray-800">{formatCurrencyValue(totalPayments)}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">寬限期數</div>
            <div className="font-bold text-orange-600">{gracePayments.length} 期</div>
          </div>
        </div>

        {gracePayments.length > 0 && (
          <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="text-center">
              <div className="text-gray-600">寬限期月付金</div>
              <div className="font-bold text-orange-600">
                {gracePayments.length > 0 ? formatCurrencyValue(gracePayments[0].totalPayment) : '-'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-gray-600">正常期月付金</div>
              <div className="font-bold text-green-600">
                {normalPayments.length > 0 ? formatCurrencyValue(normalPayments[0].totalPayment) : '-'}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (payments.length === 0) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-table text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-500 text-lg">請先輸入房貸資訊並計算</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <Toolbar
        className="mb-4 border-none bg-transparent p-0"
        start={leftToolbarTemplate}

      />

      <DataTable
        value={payments}
        loading={loading}
        paginator
        rows={600}
        rowsPerPageOptions={[12, 24, 36, 60, 84, 100, 120, 180, 240, 360, 480]}
        className="p-datatable-sm"
        scrollable
        scrollHeight="600px"
        footer={footerTemplate}
        emptyMessage="無還款計劃資料"
        rowClassName={(rowData) => rowData.isGracePeriod ? 'bg-orange-50' : ''}
      >
        <Column
          field="month"
          header="期數"
          body={monthTemplate}
          sortable
          style={{ minWidth: '120px' }}
          className="text-center"
        />
        <Column
          field="interest"
          header="本期應繳利息"
          body={interestTemplate}
          sortable
          style={{ minWidth: '120px' }}
          className="text-right"
        />
        <Column
          field="principal"
          header="本期應還本金"
          body={principalTemplate}
          sortable
          style={{ minWidth: '120px' }}
          className="text-right"
        />
        <Column
          field="totalPayment"
          header="本息合計"
          body={totalPaymentTemplate}
          sortable
          style={{ minWidth: '120px' }}
          className="text-right"
        />
        <Column
          field="remainingBalance"
          header="剩餘本金"
          body={remainingBalanceTemplate}
          sortable
          style={{ minWidth: '120px' }}
          className="text-right"
        />
      </DataTable>
    </Card>
  );
}
