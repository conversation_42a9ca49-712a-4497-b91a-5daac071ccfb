'use client';

import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { ProgressBar } from 'primereact/progressbar';
import { CreditLoanResult } from '@/lib/types/creditLoan';
import { formatCurrency, formatNumber, roundTo } from '@/lib/calculations/utils';

interface CreditLoanSummaryProps {
  result: CreditLoanResult | null;
  loading?: boolean;
}

export default function CreditLoanSummary({ result }: CreditLoanSummaryProps) {
  if (!result) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-chart-bar text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-500 text-lg">計算結果將顯示在這裡</p>
        </div>
      </Card>
    );
  }

  const { summary } = result;

  // 計算利息佔總還款的比例
  const interestRatio = (summary.totalInterest / summary.totalAmount) * 100;
  const principalRatio = (summary.totalPrincipal / summary.totalAmount) * 100;
  const feesRatio = (summary.totalFees / summary.totalAmount) * 100;

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 主要統計數據 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 總還款金額 */}
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-800 mb-2">總還款金額</h3>
                <p className="text-3xl font-bold text-blue-900">
                  {formatCurrency(summary.totalAmount)}
                </p>
              </div>
              <i className="pi pi-money-bill text-4xl text-blue-600"></i>
            </div>
          </div>

          {/* 月付金 */}
          {(summary.rateType === 'single') && (
            < div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-green-800 mb-2">月付金</h3>
                  <p className="text-3xl font-bold text-green-900">
                    {formatCurrency(summary.monthlyPayment)}
                  </p>
                </div>
                <i className="pi pi-calendar text-4xl text-green-600"></i>
              </div>
            </div>
          )
          }
          {/* 平均月付金 */}
          {(summary.rateType !== 'single') && (
            <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-green-800 mb-2">平均月付金</h3>
                  <p className="text-3xl font-bold text-green-900">
                    {formatCurrency(summary.averageMonthlyPayment)}
                  </p>
                </div>
                <i className="pi pi-calendar text-4xl text-green-600"></i>
              </div>
            </div>
          )}
        </div>

        <Divider />

        {/* 詳細統計 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 總本金 */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-gray-600 text-sm mb-2">總本金</div>
            <div className="text-xl font-bold text-gray-800">
              {formatCurrency(summary.totalPrincipal)}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              佔比 {formatNumber(principalRatio, 1)}%
            </div>
          </div>

          {/* 總利息 */}
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-red-600 text-sm mb-2">總利息</div>
            <div className="text-xl font-bold text-red-700">
              {formatCurrency(summary.totalInterest)}
            </div>
            <div className="text-xs text-red-500 mt-1">
              佔比 {formatNumber(interestRatio, 1)}%
            </div>
          </div>

          {/* 總費用 */}
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-orange-600 text-sm mb-2">總費用</div>
            <div className="text-xl font-bold text-orange-700">
              {formatCurrency(summary.totalFees)}
            </div>
            <div className="text-xs text-orange-500 mt-1">
              佔比 {formatNumber(feesRatio, 1)}%
            </div>
          </div>

          {/* 總費用年百分利 */}
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-purple-600 text-sm mb-2">總費用年百分利</div>
            <div className="text-xl font-bold text-purple-700">
              {formatNumber(summary.effectiveAnnualRate, 2)}%
            </div>
            <div className="text-xs text-purple-500 mt-1">
              APR
            </div>
          </div>
        </div>

        <Divider />

        {/* 還款結構分析 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">還款結構分析</h3>

          {/* 本金比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">本金</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(summary.totalPrincipal)} ({formatNumber(principalRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(principalRatio, 2)}
              className="h-3"
              color="#3B82F6"
            />
          </div>

          {/* 利息比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">利息</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(summary.totalInterest)} ({formatNumber(interestRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(interestRatio, 2)}
              className="h-3"
              color="#EF4444"
            />
          </div>

          {/* 費用比例 */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">費用</span>
              <span className="text-sm text-gray-600">
                {formatCurrency(summary.totalFees)} ({formatNumber(feesRatio, 1)}%)
              </span>
            </div>
            <ProgressBar
              value={roundTo(feesRatio, 2)}
              className="h-3"
              color="#F97316"
            />
          </div>
        </div>

        <Divider />

        {/* 重要提醒 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-yellow-600 mt-1"></i>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-2">重要提醒：</p>
              <ul className="space-y-1 text-xs">
                <li>• 以上計算結果僅供參考，實際條件請以銀行核准為準</li>
                <li>• 總費用年百分利(APR)已包含所有相關費用</li>
                <li>• 利率可能因個人信用狀況而有所調整</li>
                <li>• 建議多家銀行比較，選擇最適合的方案</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 快速比較指標 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-3">快速比較指標</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-blue-600 font-medium">利息負擔率</div>
              <div className="text-2xl font-bold text-blue-800">
                {formatNumber(interestRatio, 1)}%
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {interestRatio < 20 ? '負擔較輕' : interestRatio < 30 ? '負擔適中' : '負擔較重'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-medium">費用率</div>
              <div className="text-2xl font-bold text-blue-800">
                {formatNumber(feesRatio, 1)}%
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {feesRatio < 3 ? '費用較低' : feesRatio < 5 ? '費用適中' : '費用較高'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-medium">總費用年百分利</div>
              <div className="text-2xl font-bold text-blue-800">
                {formatNumber(summary.effectiveAnnualRate, 2)}%
              </div>
              <div className="text-xs text-blue-600 mt-1">
                {summary.effectiveAnnualRate < 5 ? '利率優惠' : summary.effectiveAnnualRate < 8 ? '利率合理' : '利率偏高'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
