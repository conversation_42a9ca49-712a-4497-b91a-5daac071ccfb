'use client';

import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { ProgressBar } from 'primereact/progressbar';
import { Tag } from 'primereact/tag';
import {
  InvestmentType,
  SimpleInterestResult,
  CompoundInterestResult,
  RegularInvestmentResult
} from '@/lib/types/investment';
import { formatCurrency, formatNumber, roundTo } from '@/lib/calculations/utils';

interface InvestmentResultProps {
  type: InvestmentType | null;
  result: SimpleInterestResult | CompoundInterestResult | RegularInvestmentResult | null;
  loading?: boolean;
}

export default function InvestmentResult({ type, result, loading = false }: InvestmentResultProps) {
  if (!result || !type) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-chart-line text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-500 text-lg">投資回報分析將顯示在這裡</p>
        </div>
      </Card>
    );
  }

  const getTypeLabel = (type: InvestmentType) => {
    switch (type) {
      case 'simple': return '單利投資';
      case 'compound': return '複利投資';
      case 'regular': return '定期定額';
      default: return '投資';
    }
  };

  const getReturnTagSeverity = (returnRate: number) => {
    if (returnRate < 20) return 'info';
    if (returnRate < 50) return 'success';
    if (returnRate < 100) return 'warning';
    return 'danger';
  };

  const renderSimpleResult = (result: SimpleInterestResult) => (
    <div className="space-y-6">
      {/* 主要結果 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">到期本息總額</h3>
              <p className="text-3xl font-bold text-blue-900">
                {formatCurrency(result.finalAmount)}
              </p>
            </div>
            <i className="pi pi-money-bill text-4xl text-blue-600"></i>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-green-800 mb-2">利息收入</h3>
              <p className="text-3xl font-bold text-green-900">
                {formatCurrency(result.totalInterest)}
              </p>
            </div>
            <i className="pi pi-chart-line text-4xl text-green-600"></i>
          </div>
        </div>
      </div>

      {/* 詳細統計 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-gray-600 text-sm mb-2">投入本金</div>
          <div className="text-xl font-bold text-gray-800">
            {formatCurrency(result.principal)}
          </div>
        </div>

        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <div className="text-purple-600 text-sm mb-2">投資報酬率</div>
          <div className="text-xl font-bold text-purple-700">
            {formatNumber(result.returnRate, 2)}%
          </div>
          <Tag
            value={result.returnRate < 20 ? '穩健' : result.returnRate < 50 ? '良好' : '優異'}
            severity={getReturnTagSeverity(result.returnRate)}
            className="mt-1"
          />
        </div>

        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-orange-600 text-sm mb-2">年化報酬率</div>
          <div className="text-xl font-bold text-orange-700">
            {formatNumber(result.annualizedReturn, 2)}%
          </div>
        </div>

        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-yellow-600 text-sm mb-2">利息佔比</div>
          <div className="text-xl font-bold text-yellow-700">
            {formatNumber((result.totalInterest / result.finalAmount) * 100, 1)}%
          </div>
        </div>
      </div>
    </div>
  );

  const renderCompoundResult = (result: CompoundInterestResult) => (
    <div className="space-y-6">
      {/* 主要結果 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">複利最終金額</h3>
              <p className="text-3xl font-bold text-blue-900">
                {formatCurrency(result.finalAmount)}
              </p>
            </div>
            <i className="pi pi-money-bill text-4xl text-blue-600"></i>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-lg border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-purple-800 mb-2">複利效應</h3>
              <p className="text-3xl font-bold text-purple-900">
                {formatCurrency(result.compoundEffect)}
              </p>
              <p className="text-sm text-purple-700 mt-1">
                比單利多 {formatNumber(result.comparison.compoundAdvantage, 1)}%
              </p>
            </div>
            <i className="pi pi-trending-up text-4xl text-purple-600"></i>
          </div>
        </div>
      </div>

      {/* 複利 vs 單利比較 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 mb-3">複利 vs 單利比較</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-yellow-600 font-medium">複利結果</div>
            <div className="text-2xl font-bold text-yellow-800">
              {formatCurrency(result.finalAmount)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-yellow-600 font-medium">單利結果</div>
            <div className="text-2xl font-bold text-yellow-800">
              {formatCurrency(result.comparison.simpleInterest)}
            </div>
          </div>
        </div>
        <div className="mt-3 text-center">
          <div className="text-yellow-600 text-sm">複利優勢</div>
          <div className="text-lg font-bold text-yellow-800">
            +{formatCurrency(result.compoundEffect)} ({formatNumber(result.comparison.compoundAdvantage, 1)}%)
          </div>
        </div>
      </div>

      {/* 詳細統計 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-gray-600 text-sm mb-2">投入本金</div>
          <div className="text-xl font-bold text-gray-800">
            {formatCurrency(result.principal)}
          </div>
        </div>

        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-green-600 text-sm mb-2">總利息收入</div>
          <div className="text-xl font-bold text-green-700">
            {formatCurrency(result.totalInterest)}
          </div>
        </div>

        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <div className="text-purple-600 text-sm mb-2">投資報酬率</div>
          <div className="text-xl font-bold text-purple-700">
            {formatNumber(result.returnRate, 2)}%
          </div>
        </div>

        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-orange-600 text-sm mb-2">年化報酬率</div>
          <div className="text-xl font-bold text-orange-700">
            {formatNumber(result.annualizedReturn, 2)}%
          </div>
        </div>
      </div>
    </div>
  );

  const renderRegularResult = (result: RegularInvestmentResult) => (
    <div className="space-y-6">
      {/* 主要結果 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">最終投資金額</h3>
              <p className="text-3xl font-bold text-blue-900">
                {formatCurrency(result.finalAmount)}
              </p>
            </div>
            <i className="pi pi-money-bill text-4xl text-blue-600"></i>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-green-800 mb-2">總獲利</h3>
              <p className="text-3xl font-bold text-green-900">
                {formatCurrency(result.totalReturn)}
              </p>
            </div>
            <i className="pi pi-chart-line text-4xl text-green-600"></i>
          </div>
        </div>
      </div>

      {/* 投資摘要 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-3">投資摘要</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-blue-600 font-medium">總投入金額</div>
            <div className="text-2xl font-bold text-blue-800">
              {formatCurrency(result.totalInvestment)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-blue-600 font-medium">總期數</div>
            <div className="text-2xl font-bold text-blue-800">
              {result.summary.totalPeriods}
            </div>
          </div>
          <div className="text-center">
            <div className="text-blue-600 font-medium">平均餘額</div>
            <div className="text-2xl font-bold text-blue-800">
              {formatCurrency(result.summary.averageBalance)}
            </div>
          </div>
        </div>
      </div>

      {/* 詳細統計 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-gray-600 text-sm mb-2">投資報酬率</div>
          <div className="text-xl font-bold text-gray-800">
            {formatNumber(result.returnRate, 2)}%
          </div>
        </div>

        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-orange-600 text-sm mb-2">年化報酬率</div>
          <div className="text-xl font-bold text-orange-700">
            {formatNumber(result.annualizedReturn, 2)}%
          </div>
        </div>

        <div className="text-center p-4 bg-purple-50 rounded-lg">
          <div className="text-purple-600 text-sm mb-2">有效利率</div>
          <div className="text-xl font-bold text-purple-700">
            {formatNumber(result.summary.effectiveRate, 2)}%
          </div>
        </div>

        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-yellow-600 text-sm mb-2">獲利佔比</div>
          <div className="text-xl font-bold text-yellow-700">
            {formatNumber((result.totalReturn / result.finalAmount) * 100, 1)}%
          </div>
        </div>
      </div>

      {/* 投資結構分析 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">投資結構分析</h3>

        {/* 本金比例 */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">投入本金</span>
            <span className="text-sm text-gray-600">
              {formatCurrency(result.totalInvestment)} ({formatNumber((result.totalInvestment / result.finalAmount) * 100, 1)}%)
            </span>
          </div>
          <ProgressBar
            value={roundTo((result.totalInvestment / result.finalAmount) * 100, 2)}
            className="h-3"
            color="#3B82F6"
          />
        </div>

        {/* 獲利比例 */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">投資獲利</span>
            <span className="text-sm text-gray-600">
              {formatCurrency(result.totalReturn)} ({formatNumber((result.totalReturn / result.finalAmount) * 100, 1)}%)
            </span>
          </div>
          <ProgressBar
            value={roundTo((result.totalReturn / result.finalAmount) * 100, 2)}
            className="h-3"
            color="#10B981"
          />
        </div>
      </div>
    </div>
  );

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 標題 */}
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{getTypeLabel(type)}結果</h3>
          <p className="text-gray-600">詳細的投資回報分析</p>
        </div>

        <Divider />

        {/* 動態結果顯示 */}
        {type === 'simple' && renderSimpleResult(result as SimpleInterestResult)}
        {type === 'compound' && renderCompoundResult(result as CompoundInterestResult)}
        {type === 'regular' && renderRegularResult(result as RegularInvestmentResult)}

        <Divider />

        {/* 重要提醒 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-yellow-600 mt-1"></i>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-2">重要提醒：</p>
              <ul className="space-y-1 text-xs">
                <li>• 以上計算結果僅供參考，實際投資報酬可能因市場變化而不同</li>
                <li>• 投資有風險，過去績效不代表未來表現</li>
                <li>• 建議分散投資降低風險</li>
                <li>• 投資前請詳閱相關說明書並評估自身風險承受能力</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
