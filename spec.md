# 貸款計算機 App 開發規劃書

## 1. 專案概述

### 1.1 專案名稱
貸款計算機 (Loan Calculator)

### 1.2 專案目標
開發一個功能完整的貸款計算器PWA應用，支援信貸和房貸計算，提供清晰的還款計劃表格和總額統計。

### 1.3 目標用戶
- 個人用戶：需要計算貸款還款計劃的一般民眾
- 金融從業人員：銀行專員、保險代理人、理財顧問
- 房地產從業人員：房仲、代銷人員

## 2. 技術架構

### 2.1 技術棧
```json
{
  "框架": "Next.js 15.4.2",
  "UI庫": "PrimeReact 10.9.6",
  "圖標": "PrimeIcons 7.0.0",
  "樣式": "Tailwind CSS 4",
  "語言": "TypeScript 5",
  "PWA": "next-pwa",
  "部署": "out export 靜態部署",
  "代碼檢查": "ESLint 9"
}
```

### 2.2 專案結構
```
src/
├── app/
│   ├── layout.tsx              # 主佈局
│   ├── page.tsx                # 首頁
│   ├── credit-loan/            # 信貸計算頁面
│   │   └── page.tsx
│   ├── mortgage/               # 房貸計算頁面
│   │   └── page.tsx
│   └── globals.css
├── components/
│   ├── common/                 # 通用組件
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── NavigationMenu.tsx
│   ├── credit-loan/            # 信貸相關組件
│   │   ├── CreditLoanForm.tsx
│   │   ├── CreditLoanTable.tsx
│   │   └── InterestRateSegment.tsx
│   └── mortgage/               # 房貸相關組件
│       ├── MortgageForm.tsx
│       ├── MortgageTable.tsx
│       └── MortgageChart.tsx
├── lib/
│   ├── calculations/           # 計算邏輯
│   │   ├── creditLoan.ts
│   │   ├── mortgage.ts
│   │   └── utils.ts
│   └── types/                  # TypeScript 類型定義
│       ├── creditLoan.ts
│       └── mortgage.ts
└── styles/
    └── components.css
```

## 3. 功能規劃

### 3.1 信貸計算 (Credit Loan)

#### 3.1.1 正向計算 - 輸入欄位
- **貸款金額**: 數字輸入框，支援千分位顯示
- **期數(月)**: 數字輸入框，範圍 1-360 個月
- **利率計算方式**: 選擇器 (單一利率 / 2段式 / 3段式)
- **相關費用**:
  - 開辦費 (金額)
  - 帳管費 (每月)
  - 其他費用 (一次性)

#### 3.1.2 利率回推功能
- **功能說明**: 根據已知的貸款條件反推利率
- **輸入欄位**:
  - 貸款金額 (元)
  - 月付金額 (元)
  - 期數 (月)
  - 相關費用 (開辦費、帳管費、其他費用)
- **輸出結果**:
  - 名目年利率 (%)
  - 實際年利率 APR (%)
  - 總利息支出
  - 總費用支出
  - 利率合理性評估
- **計算方式**: 使用數值方法 (牛頓法或二分法) 求解利率

#### 3.1.3 利率設定
- **單一利率**: 一個利率輸入框
- **2段式利率**: 
  - 第1段: 第1月~第N月 利率X%
  - 第2段: 第(N+1)月~最後一期 利率Y%
- **3段式利率**:
  - 第1段: 第1月~第N月 利率X%
  - 第2段: 第(N+1)月~第M月 利率Y%
  - 第3段: 第(M+1)月~最後一期 利率Z%

#### 3.1.4 輸出結果
- **還款計劃表格**:
  | 期數 | 本期應繳利息 | 本期應還本金 | 本息合計 | 剩餘本金 |
  |------|-------------|-------------|----------|----------|
  | 1    | XXX         | XXX         | XXX      | XXX      |
  
- **總額統計**:
  - 總利息
  - 總本息
  - 總費用
  - 總費用年百分利

### 3.2 投資回報計算 (Investment Return)

#### 3.2.1 功能說明
- **目的**: 計算投資的預期回報和複利效應
- **適用場景**: 定存、基金定期定額、股票投資等理財規劃

#### 3.2.2 單利計算
- **輸入欄位**:
  - 本金 (元)
  - 年利率 (%) 或 月利率 (%)
  - 投資年期 (年) 或 月期 (月)
- **輸出結果**:
  - 到期本息總額
  - 利息收入
  - 投資報酬率
  - 年化報酬率

#### 3.2.3 複利計算
- **輸入欄位**:
  - 初始本金 (元)
  - 年利率 (%) 或 月利率 (%)
  - 投資年期 (年) 或 月期 (月)
  - 複利頻率 (年複利/月複利/日複利)
- **輸出結果**:
  - 到期本息總額
  - 利息收入
  - 複利效應分析
  - 與單利比較

#### 3.2.4 定期定額投資
- **輸入欄位**:
  - 初始本金 (元)
  - 每期投入金額 (元)
  - 投入頻率 (每月/每季/每年)
  - 年利率 (%)
  - 投資年期 (年)
- **輸出結果**:
  - 總投入金額
  - 到期本息總額
  - 總獲利
  - 年化報酬率
  - 投資成長曲線圖

#### 3.2.5 視覺化分析
- **成長曲線圖**: 顯示投資金額隨時間增長
- **複利效應圖**: 比較單利與複利差異
- **定期定額分析**: 顯示本金與獲利比例

### 3.3 房貸計算 (Mortgage)

#### 3.3.1 輸入欄位
- **房屋總價**: 數字輸入框
- **自備款**: 數字輸入框或百分比
- **貸款金額**: 自動計算 (房屋總價 - 自備款)
- **貸款年限**: 選擇器 (10/15/20/25/30/35/40年)
- **利率類型**: 固定利率 / 機動利率
- **利率**: 數字輸入框 (年利率%)
- **寬限期**: 選擇器 (0-5年)

#### 3.3.2 相關費用
- **代書費**: 固定金額
- **規費**: 按貸款金額百分比
- **火險費**: 年費
- **地震險**: 年費
- **鑑價費**: 固定金額

#### 3.3.3 輸出結果
- **還款計劃表格**: 同信貸格式
- **統計資訊**:
  - 每月還款金額
  - 總利息支出
  - 總還款金額
  - 利息佔總還款比例
- **圖表顯示**:
  - 本金與利息比例圓餅圖
  - 累積還款趨勢線圖

### 3.3 通用功能

#### 3.3.1 PWA 功能
- 離線使用
- 安裝到桌面/主畫面
- 快取計算結果
- 推播通知 (還款提醒)

#### 3.3.2 數據管理
- 本地儲存計算歷史
- 匯出計算結果 (PDF/Excel)
- 分享功能
- 收藏常用計算

#### 3.3.3 使用者體驗
- 響應式設計 (支援手機/平板/桌面)
- 深色/淺色主題切換
- 多語言支援 (繁中/簡中/英文)
- 無障礙設計

## 4. UI/UX 設計規劃

### 4.1 設計原則
- **簡潔明瞭**: 減少認知負荷
- **直觀操作**: 符合用戶習慣
- **數據視覺化**: 清楚呈現計算結果
- **行動優先**: 優化手機使用體驗

### 4.2 色彩規劃
- **主色**: 藍色系 (#1976D2) - 專業、信賴
- **輔助色**: 綠色系 (#4CAF50) - 金錢、成功
- **警告色**: 橘色系 (#FF9800) - 注意事項
- **錯誤色**: 紅色系 (#F44336) - 錯誤提示

### 4.3 頁面佈局
- **頂部導航**: Logo + 功能切換 + 設定
- **主內容區**: 表單輸入 + 結果顯示
- **底部**: 版權資訊 + 連結

## 5. 開發階段規劃

### 5.1 第一階段 - 基礎架構 (Week 1-2)
- [ ] 專案初始化與環境設定
- [ ] 基礎組件開發 (Header, Footer, Navigation)
- [ ] 路由設定
- [ ] 樣式系統建立
- [ ] PWA 基礎配置

### 5.2 第二階段 - 信貸功能 (Week 3-4)
- [ ] 信貸計算邏輯實作
- [ ] 信貸表單組件
- [ ] 利率分段邏輯
- [ ] 還款計劃表格組件
- [ ] 計算結果統計

### 5.3 第三階段 - 房貸功能 (Week 5-6)
- [ ] 房貸計算邏輯實作
- [ ] 房貸表單組件
- [ ] 寬限期處理邏輯
- [ ] 圖表組件整合
- [ ] 費用計算功能

### 5.4 第四階段 - 進階功能 (Week 7-8)
- [ ] 數據持久化
- [ ] 匯出功能
- [ ] 主題切換
- [ ] 多語言支援
- [ ] 性能優化

### 5.5 第五階段 - 測試與部署 (Week 9-10)
- [ ] 單元測試
- [ ] 整合測試
- [ ] 使用者測試
- [ ] 錯誤處理優化
- [ ] 正式部署

## 6. 技術考量

### 6.1 性能優化
- 代碼分割 (Code Splitting)
- 圖片優化
- 懶加載
- 緩存策略

### 6.2 SEO 優化
- 元標籤設定
- 結構化數據
- 網站地圖
- 頁面速度優化

### 6.3 安全性
- 輸入驗證
- XSS 防護
- 數據加密 (本地儲存)

## 7. 後續維護

### 7.1 功能擴展
- 投資型保險計算
- 車貸計算
- 企業貸款計算
- 利率比較功能

### 7.2 數據分析
- 使用者行為分析
- 功能使用統計
- 錯誤監控

### 7.3 版本更新
- 定期更新利率數據
- 新增銀行產品資訊
- 優化使用者體驗


