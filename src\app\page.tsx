import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import Link from 'next/link';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-1 py-4">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            專業金融計算工具
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            信貸、房貸、投資回報一站式計算，助您做出最佳財務決策
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-sm text-gray-500">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">信貸計算</span>
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">房貸計算</span>
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">投資回報</span>
            <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full">利率回推</span>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card className="shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-center p-6">
              <i className="pi pi-calculator text-6xl text-blue-600 mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">信貸計算</h2>
              <p className="text-gray-600 mb-4">
                支援單一利率、2段式、3段式利率計算，提供詳細還款計劃表
              </p>
              <div className="mb-4">
                <div className="flex flex-wrap justify-center gap-1 text-xs">
                  <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded">正向計算</span>
                  <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded">利率回推</span>
                </div>
              </div>
              <Link href="/credit-loan">
                <Button
                  label="開始計算"
                  icon="pi pi-arrow-right"
                  className="w-full"
                  size="large"
                />
              </Link>
            </div>
          </Card>

          <Card className="shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-center p-6">
              <i className="pi pi-building text-6xl text-green-600 mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">房貸計算</h2>
              <p className="text-gray-600 mb-4">
                計算房屋貸款還款金額，包含寬限期、各項費用，並提供圖表分析
              </p>
              <div className="mb-4">
                <div className="flex flex-wrap justify-center gap-1 text-xs">
                  <span className="bg-green-50 text-green-700 px-2 py-1 rounded">寬限期</span>
                  <span className="bg-green-50 text-green-700 px-2 py-1 rounded">圖表分析</span>
                </div>
              </div>
              <Link href="/mortgage">
                <Button
                  label="開始計算"
                  icon="pi pi-arrow-right"
                  className="w-full"
                  severity="success"
                  size="large"
                />
              </Link>
            </div>
          </Card>

          <Card className="shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-center p-6">
              <i className="pi pi-chart-line text-6xl text-purple-600 mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">投資回報</h2>
              <p className="text-gray-600 mb-4">
                計算單利、複利、定期定額投資回報，幫助制定投資策略
              </p>
              <div className="mb-4">
                <div className="flex flex-wrap justify-center gap-1 text-xs">
                  <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded">單利</span>
                  <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded">複利</span>
                  <span className="bg-purple-50 text-purple-700 px-2 py-1 rounded">定期定額</span>
                </div>
              </div>
              <Link href="/investment">
                <Button
                  label="開始計算"
                  icon="pi pi-arrow-right"
                  className="w-full"
                  severity="help"
                  size="large"
                />
              </Link>
            </div>
          </Card>
        </div>

        {/* Features List */}
        <div className="mt-16 max-w-5xl mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8">完整功能特色</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 信貸功能 */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                <i className="pi pi-calculator text-blue-600"></i>
                信貸計算
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>單一/2段式/3段式利率</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>利率回推功能</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>詳細還款計劃表</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>CSV 匯出功能</span>
                </div>
              </div>
            </div>

            {/* 房貸功能 */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
                <i className="pi pi-building text-green-600"></i>
                房貸計算
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>固定/機動利率</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>寬限期計算</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>圖表視覺化分析</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>各項費用計算</span>
                </div>
              </div>
            </div>

            {/* 投資功能 */}
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                <i className="pi pi-chart-line text-purple-600"></i>
                投資回報
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>單利/複利計算</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>定期定額投資</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>複利效應分析</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="pi pi-check text-green-500 text-xs"></i>
                  <span>投資建議評估</span>
                </div>
              </div>
            </div>
          </div>


        </div>

        {/* Quick Start Section */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 max-w-4xl mx-auto">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">立即開始使用</h3>
            <p className="text-blue-100 mb-6">
              選擇您需要的計算工具，幾分鐘內獲得專業的財務分析結果
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/credit-loan">
                <Button
                  label="信貸計算"
                  icon="pi pi-calculator"
                  className="w-full bg-white text-blue-600 border-white hover:bg-blue-50"
                  size="large"
                />
              </Link>
              <Link href="/mortgage">
                <Button
                  label="房貸計算"
                  icon="pi pi-building"
                  className="w-full bg-white text-green-600 border-white hover:bg-green-50"
                  size="large"
                />
              </Link>
              <Link href="/investment">
                <Button
                  label="投資回報"
                  icon="pi pi-chart-line"
                  className="w-full bg-white text-purple-600 border-white hover:bg-purple-50"
                  size="large"
                />
              </Link>
            </div>
          </div>
        </div>

      </main>

      <Footer />
    </div>
  );
}

