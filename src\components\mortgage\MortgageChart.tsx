'use client';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  TooltipItem
} from 'chart.js';
import { Pie, Line } from 'react-chartjs-2';
import { MortgageResult } from '@/lib/types/mortgage';
import { formatCurrency } from '@/lib/calculations/utils';

// 註冊 Chart.js 組件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface MortgageChartProps {
  result: MortgageResult | null;
  loading?: boolean;
}

export default function MortgageChart({ result }: MortgageChartProps) {
  if (!result) {
    return (
      <Card className="w-full">
        <div className="text-center py-8">
          <i className="pi pi-chart-pie text-4xl text-gray-400 mb-4"></i>
          <p className="text-gray-500 text-lg">圖表分析將顯示在這裡</p>
        </div>
      </Card>
    );
  }

  const { chartData, summary } = result;

  // 本金利息比例圓餅圖數據
  const pieChartData = {
    labels: ['本金', '利息'],
    datasets: [
      {
        data: [chartData.principalVsInterest.principal, chartData.principalVsInterest.interest],
        backgroundColor: [
          '#3B82F6', // 藍色 - 本金
          '#EF4444'  // 紅色 - 利息
        ],
        borderColor: [
          '#2563EB',
          '#DC2626'
        ],
        borderWidth: 2,
        hoverBackgroundColor: [
          '#60A5FA',
          '#F87171'
        ]
      }
    ]
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 14
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: TooltipItem<'pie'>) {
            const label = context.label || '';
            const value = context.parsed;
            const total = chartData.principalVsInterest.principal + chartData.principalVsInterest.interest;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${formatCurrency(value)} (${percentage}%)`;
          }
        }
      }
    }
  };

  // 累積還款趨勢線圖數據
  const lineChartData = {
    labels: chartData.cumulativePayments.map(item => `第${item.month}期`),
    datasets: [
      {
        label: '累積本金',
        data: chartData.cumulativePayments.map(item => item.cumulativePrincipal),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4
      },
      {
        label: '累積利息',
        data: chartData.cumulativePayments.map(item => item.cumulativeInterest),
        borderColor: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: true,
        tension: 0.4
      },
      {
        label: '累積總額',
        data: chartData.cumulativePayments.map(item => item.cumulativeTotal),
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        fill: false,
        tension: 0.4,
        borderWidth: 3
      }
    ]
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: TooltipItem<'line'>) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: '還款期數'
        },
        ticks: {
          maxTicksLimit: 12 // 限制 x 軸標籤數量
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: '金額 (元)'
        },
        ticks: {
          callback: function (value: string | number) {
            return formatCurrency(Number(value));
          }
        }
      }
    }
  };

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 圖表標題 */}
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">還款分析圖表</h3>
          <p className="text-gray-600">視覺化呈現您的房貸還款結構</p>
        </div>

        {/* 本金利息比例圓餅圖 */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h4 className="text-lg font-medium text-gray-800 mb-4 text-center">本金與利息比例</h4>
          <div className="h-80">
            <Pie data={pieChartData} options={pieChartOptions} />
          </div>

          {/* 比例統計 */}
          <div className="mt-4 grid grid-cols-2 gap-4 text-center">
            <div className="bg-blue-50 p-3 rounded">
              <div className="text-blue-600 text-sm font-medium">本金總額</div>
              <div className="text-blue-800 text-lg font-bold">
                {formatCurrency(chartData.principalVsInterest.principal)}
              </div>
              <div className="text-blue-600 text-xs">
                {((chartData.principalVsInterest.principal / (chartData.principalVsInterest.principal + chartData.principalVsInterest.interest)) * 100).toFixed(1)}%
              </div>
            </div>
            <div className="bg-red-50 p-3 rounded">
              <div className="text-red-600 text-sm font-medium">利息總額</div>
              <div className="text-red-800 text-lg font-bold">
                {formatCurrency(chartData.principalVsInterest.interest)}
              </div>
              <div className="text-red-600 text-xs">
                {((chartData.principalVsInterest.interest / (chartData.principalVsInterest.principal + chartData.principalVsInterest.interest)) * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* 累積還款趨勢線圖 */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h4 className="text-lg font-medium text-gray-800 mb-4 text-center">累積還款趨勢</h4>
          <div className="h-96">
            <Line data={lineChartData} options={lineChartOptions} />
          </div>

          {/* 趨勢說明 */}
          <div className="mt-4 bg-white p-4 rounded border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span className="font-medium">累積本金</span>
                </div>
                <p className="text-gray-600">隨時間增加的本金償還金額</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="font-medium">累積利息</span>
                </div>
                <p className="text-gray-600">隨時間增加的利息支付金額</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="font-medium">累積總額</span>
                </div>
                <p className="text-gray-600">本金加利息的總償還金額</p>
              </div>
            </div>
          </div>
        </div>

        {/* 重要指標 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <i className="pi pi-info-circle text-yellow-600 mt-1"></i>
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-2">圖表解讀：</p>
              <ul className="space-y-1 text-xs">
                <li>• 圓餅圖顯示整個貸款期間本金與利息的比例關係</li>
                <li>• 線圖顯示隨時間推移的累積還款趨勢</li>
                <li>• 利息佔比 {summary.interestRatio.toFixed(1)}%，{summary.interestRatio < 30 ? '負擔合理' : '利息負擔較重'}</li>
                <li>• 建議定期檢視利率變化，考慮提前還款或轉貸</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
